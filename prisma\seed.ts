import { PrismaClient, Priority } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Create users
  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
    },
  });

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
    },
  });

  // Create tasks
  await prisma.task.createMany({
    data: [
      {
        title: 'Complete project proposal',
        description: 'Write and submit the Q1 project proposal',
        priority: Priority.HIGH,
        dueDate: new Date('2024-12-31'),
        userId: user1.id,
      },
      {
        title: 'Review code changes',
        description: 'Review the latest pull requests',
        priority: Priority.MEDIUM,
        completed: true,
        userId: user1.id,
      },
      {
        title: 'Update documentation',
        description: 'Update the API documentation with new endpoints',
        priority: Priority.LOW,
        userId: user2.id,
      },
      {
        title: 'Fix critical bug',
        description: 'Fix the authentication issue in production',
        priority: Priority.URGENT,
        dueDate: new Date('2024-12-15'),
        userId: user2.id,
      },
      {
        title: 'Team meeting preparation',
        description: 'Prepare agenda and materials for weekly team meeting',
        priority: Priority.MEDIUM,
        dueDate: new Date('2024-12-20'),
        userId: user1.id,
      },
    ],
  });

  console.log('Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
