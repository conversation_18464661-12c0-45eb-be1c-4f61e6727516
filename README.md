# Task Management System

A modern, full-stack task management application built with React, TypeScript, Tailwind CSS, and a local SQLite database.

## Features

- ✅ **Modern Tech Stack**: React 19 + TypeScript + Vite + Tailwind CSS
- 🗄️ **Local Database**: SQLite with Prisma ORM
- 🚀 **Fast Development**: Hot reload for both frontend and backend
- 📱 **Responsive Design**: Mobile-friendly interface with Tailwind CSS
- 🔄 **Real-time Updates**: Instant task updates and status changes
- 🎯 **Task Management**: Create, edit, delete, and organize tasks
- 👥 **User Management**: Multi-user support with task assignment
- 🏷️ **Priority System**: LOW, MEDIUM, HIGH, URGENT priority levels
- 📅 **Due Dates**: Set and track task deadlines
- 📊 **Dashboard**: Overview with task statistics
- 🔍 **Filtering**: Filter tasks by status and priority

## Tech Stack

### Frontend
- **React 19** - Modern React with latest features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework

### Backend
- **Express.js** - Web application framework
- **TypeScript** - Type-safe server development
- **Prisma** - Modern database toolkit
- **SQLite** - Local database (perfect for development)

### Development Tools
- **tsx** - TypeScript execution engine
- **nodemon** - Auto-restart server on changes
- **concurrently** - Run multiple commands simultaneously

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### Installation & Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Set up the database**:
   ```bash
   # Generate Prisma client
   npx prisma generate

   # Create and migrate database
   npx prisma db push

   # Seed with sample data
   npm run db:seed
   ```

3. **Start development servers**:
   ```bash
   npm run dev
   ```

   This will start both:
   - Frontend: http://localhost:5173 (or next available port)
   - Backend API: http://localhost:3001

## Available Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run dev:client` - Start only the frontend (Vite)
- `npm run dev:server` - Start only the backend (Express)
- `npm run build` - Build for production
- `npm run db:seed` - Seed database with sample data
- `npm run db:studio` - Open Prisma Studio (database GUI)

## API Endpoints

### Tasks
- `GET /api/tasks` - Get all tasks
- `POST /api/tasks` - Create a new task
- `PUT /api/tasks/:id` - Update a task
- `DELETE /api/tasks/:id` - Delete a task

### Users
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user

### System
- `GET /api/health` - Health check
- `GET /` - API information

## Database Schema

### User
- `id` - Unique identifier
- `email` - User email (unique)
- `name` - User display name
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

### Task
- `id` - Unique identifier
- `title` - Task title
- `description` - Optional task description
- `completed` - Completion status
- `priority` - Priority level (LOW, MEDIUM, HIGH, URGENT)
- `dueDate` - Optional due date
- `userId` - Assigned user ID
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

## Project Structure

```
├── src/
│   ├── components/          # React components
│   │   ├── TaskCard.tsx     # Individual task display
│   │   └── TaskForm.tsx     # Task creation/editing form
│   ├── types/               # TypeScript type definitions
│   ├── api/                 # API client functions
│   ├── App.tsx              # Main application component
│   └── main.tsx             # Application entry point
├── server/
│   ├── index.ts             # Express server
│   └── tsconfig.json        # Server TypeScript config
├── prisma/
│   ├── schema.prisma        # Database schema
│   └── seed.ts              # Database seeding script
└── public/                  # Static assets
```

## Development

### Adding New Features

1. **Database Changes**: Update `prisma/schema.prisma` and run `npx prisma db push`
2. **API Endpoints**: Add routes in `server/index.ts`
3. **Frontend Components**: Create components in `src/components/`
4. **Types**: Update type definitions in `src/types/`

### Database Management

- **View Data**: `npm run db:studio` opens Prisma Studio
- **Reset Database**: Delete `dev.db` and run `npx prisma db push && npm run db:seed`
- **Schema Changes**: Update `schema.prisma` and run `npx prisma db push`

## Production Deployment

1. **Build the frontend**:
   ```bash
   npm run build
   ```

2. **Set up production database** (PostgreSQL, MySQL, etc.)

3. **Update environment variables** for production

4. **Deploy** using your preferred platform (Vercel, Netlify, Railway, etc.)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).
