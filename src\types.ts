enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

interface CreateTaskData {
  title: string;
  description?: string;
  priority: Priority;
  dueDate?: string;
  userId: number;
}

interface UpdateTaskData {
  title?: string;
  description?: string;
  completed?: boolean;
  priority?: Priority;
  dueDate?: string;
}

interface User {
  id: number;
  email: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  tasks?: Task[];
}

interface Task {
  id: number;
  title: string;
  description?: string;
  completed: boolean;
  priority: Priority;
  dueDate?: string;
  createdAt: string;
  updatedAt: string;
  userId: number;
  user?: User;
}

export { Priority, CreateTaskData, UpdateTaskData, User, Task };
