function TestApp() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: 'blue' }}>Test Page</h1>
      <p>If you can see this, React is working!</p>
      <div style={{ 
        backgroundColor: '#f0f0f0', 
        padding: '10px', 
        border: '1px solid #ccc',
        marginTop: '20px'
      }}>
        <h2>System Status:</h2>
        <ul>
          <li>✅ React is rendering</li>
          <li>✅ TypeScript is compiling</li>
          <li>✅ Vite dev server is running</li>
        </ul>
      </div>
      <button 
        onClick={() => alert('Button clicked!')}
        style={{
          backgroundColor: '#007bff',
          color: 'white',
          padding: '10px 20px',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          marginTop: '20px'
        }}
      >
        Test Button
      </button>
    </div>
  );
}

export default TestApp;
