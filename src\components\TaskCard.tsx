import { Task, Priority } from '../types/index';

interface TaskCardProps {
  task: Task;
  onToggleComplete: (id: number, completed: boolean) => void;
  onDelete: (id: number) => void;
  onEdit: (task: Task) => void;
}

const priorityColors = {
  [Priority.LOW]: 'bg-green-100 text-green-800 border-green-200',
  [Priority.MEDIUM]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  [Priority.HIGH]: 'bg-orange-100 text-orange-800 border-orange-200',
  [Priority.URGENT]: 'bg-red-100 text-red-800 border-red-200',
};

export default function TaskCard({ task, onToggleComplete, onDelete, onEdit }: TaskCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && !task.completed;

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${
      task.completed 
        ? 'border-green-500 opacity-75' 
        : isOverdue 
        ? 'border-red-500' 
        : 'border-blue-500'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <input
            type="checkbox"
            checked={task.completed}
            onChange={(e) => onToggleComplete(task.id, e.target.checked)}
            className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <div className="flex-1">
            <h3 className={`text-lg font-semibold ${
              task.completed ? 'line-through text-gray-500' : 'text-gray-900'
            }`}>
              {task.title}
            </h3>
            {task.description && (
              <p className={`mt-1 text-sm ${
                task.completed ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {task.description}
              </p>
            )}
            <div className="mt-3 flex items-center space-x-4 text-sm">
              <span className={`px-2 py-1 rounded-full text-xs font-medium border ${
                priorityColors[task.priority]
              }`}>
                {task.priority}
              </span>
              {task.dueDate && (
                <span className={`text-xs ${
                  isOverdue ? 'text-red-600 font-semibold' : 'text-gray-500'
                }`}>
                  Due: {formatDate(task.dueDate)}
                  {isOverdue && ' (Overdue)'}
                </span>
              )}
              <span className="text-xs text-gray-500">
                Assigned to: {task.user?.name}
              </span>
            </div>
          </div>
        </div>
        <div className="flex space-x-2 ml-4">
          <button
            onClick={() => onEdit(task)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Edit
          </button>
          <button
            onClick={() => onDelete(task.id)}
            className="text-red-600 hover:text-red-800 text-sm font-medium"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}
